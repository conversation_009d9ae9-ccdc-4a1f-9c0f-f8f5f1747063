<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="projectlombok.org" />
      <option name="name" value="projectlombok.org" />
      <option name="url" value="https://projectlombok.org/repository" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="spring-snapshots" />
      <option name="name" value="spring-snapshots" />
      <option name="url" value="https://repo.spring.io/snapshot" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jitpack.io" />
      <option name="name" value="jitpack.io" />
      <option name="url" value="https://jitpack.io" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="github" />
      <option name="name" value="GitHub jsimone Webapp Runner" />
      <option name="url" value="https://raw.github.com/jsimone/webapp-runner/mvn-repo/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="apache" />
      <option name="name" value="apache" />
      <option name="url" value="https://repository.apache.org/content/repositories/releases/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="huaweicloud" />
      <option name="name" value="huaweicloud" />
      <option name="url" value="https://mirrors.huaweicloud.com/repository/maven" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="https://maven.aliyun.com/repository/public" />
    </remote-repository>
  </component>
</project>