# Code Style Guide for Photo Upload System

## Java Code Style

### 1. Naming Conventions
- Classes: PascalCase (e.g., `PhotoController`)
- Methods and variables: camelCase (e.g., `uploadFile`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_FILE_SIZE`)
- Packages: lowercase with dots (e.g., `com.photoapp.core`)

### 2. File Organization
- One top-level class per file
- Static imports before other imports
- Imports ordered: java.*, javax.*, org.*, com.*
- Related methods grouped together
- Public methods before private ones

### 3. Documentation
- All public APIs must have JavaDoc
- Include @param, @return, and @throws in method documentation
- Keep comments current and relevant
- Document non-obvious implementation decisions

### 4. Exception Handling
- Use specific exception types
- Always log exceptions with context
- Don't catch Exception (use specific types)
- Use try-with-resources for AutoCloseable resources

### 5. Testing
- Test class name should match class being tested with "Test" suffix
- One logical concept per test method
- Use descriptive test method names: methodName_scenario_expectedBehavior
- Follow AAA pattern: Arrange, Act, Assert

### 6. Code Format
```java
// Correct brace style
if (condition) {
    doSomething();
}

// Proper spacing
for (int i = 0; i < limit; i++) {
    // code
}

// Method structure
public void methodName(
        @NotNull String param1,
        Optional<String> param2) {
    // implementation
}
```

### 7. Best Practices
- Prefer immutability when possible
- Use dependency injection
- Keep methods small and focused
- Avoid null references, use Optional
- Use lombok annotations judiciously

### 8. Security
- Validate all user input
- Sanitize file names and paths
- Use parameterized queries
- Apply principle of least privilege
- Log security-relevant events

### 9. Performance
- Use batch operations for multiple files
- Implement proper caching strategies
- Close resources in finally blocks or use try-with-resources
- Avoid premature optimization

### 10. API Design
- Use RESTful conventions
- Version your APIs
- Use proper HTTP methods
- Return appropriate status codes
- Include error details in responses

### 11. Configuration
- Externalize configuration
- Use meaningful property names
- Document all configuration options
- Provide sensible defaults

### 12. Logging
- Use appropriate log levels
- Include contextual information
- Don't log sensitive data
- Use structured logging when possible

## Git Practices

### 1. Commit Messages
```
feat: Add image compression feature
^--^  ^----------------------^
|     |
|     +-> Summary in present tense
|
+-------> Type: feat, fix, docs, style, refactor, test, chore
```

### 2. Branch Naming
- feature/description-of-feature
- bugfix/issue-description
- hotfix/critical-issue-fix
- release/version-number

### 3. Pull Requests
- Include description of changes
- Reference related issues
- Include testing instructions
- Update documentation if needed

## Project Structure
```
src/
  main/
    java/
      com.photoapp.core/
        config/       # Configuration classes
        controller/  # REST controllers
        model/      # Domain models
        repository/ # Data access
        service/    # Business logic
        exception/  # Custom exceptions
    resources/     # Properties and static resources
  test/
    java/         # Test classes
    resources/    # Test resources