package com.example.exception;

import lombok.Getter;

@Getter
public class PhotoException extends RuntimeException {
    private final String code;
    private final String message;

    public PhotoException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public static class ErrorCode {
        public static final String FILE_TOO_LARGE = "FILE_TOO_LARGE";
        public static final String INVALID_FILE_TYPE = "INVALID_FILE_TYPE";
        public static final String FILE_NOT_FOUND = "FILE_NOT_FOUND";
        public static final String UPLOAD_FAILED = "UPLOAD_FAILED";
        public static final String DOWNLOAD_FAILED = "DOWNLOAD_FAILED";
    }
}
