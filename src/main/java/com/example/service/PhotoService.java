package com.example.service;

import com.example.entity.Photo;
import com.example.repository.PhotoRepository;
import com.example.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PhotoService {

    private final PhotoRepository photoRepository;

    @Value("${photo.upload.path}")
    private String uploadPath;

    @Value("${photo.upload.allowed-types}")
    private String allowedTypes;

    @Value("${photo.upload.max-size}")
    private long maxSize;

    @Transactional
    public Photo upload(MultipartFile file) throws IOException {
        // 验证文件
        validateFile(file);

        // 生成唯一文件名
        String fileName = UUID.randomUUID().toString() + "." + FileUtils.getFileExtension(file.getOriginalFilename());
        
        // 创建存储路径
        Path targetPath = Paths.get(uploadPath, fileName);
        if (!Files.exists(targetPath.getParent())) {
            Files.createDirectories(targetPath.getParent());
        }

        // 保存文件
        Files.copy(file.getInputStream(), targetPath);

        // 创建Photo实体
        Photo photo = new Photo();
        photo.setOriginalName(file.getOriginalFilename());
        photo.setFileName(fileName);
        photo.setContentType(file.getContentType());
        photo.setSize(file.getSize());
        photo.setUploadTime(LocalDateTime.now());
        photo.setUploadIp(FileUtils.getRemoteIp());
        photo.setStoragePath(targetPath.toString());
        photo.setIsPublic(false);
        photo.setDownloadCount(0);
        photo.setDeleted(false);

        return photoRepository.save(photo);
    }

    @Transactional
    public List<Photo> uploadMultiple(MultipartFile[] files) throws IOException {
        return java.util.Arrays.stream(files)
                .map(this::upload)
                .toList();
    }

    public byte[] download(String fileName) throws IOException {
        Photo photo = photoRepository.findByFileNameAndDeletedFalse(fileName)
                .orElseThrow(() -> new RuntimeException("File not found: " + fileName));

        // 更新下载计数
        photo.setDownloadCount(photo.getDownloadCount() + 1);
        photoRepository.save(photo);

        return Files.readAllBytes(Paths.get(photo.getStoragePath()));
    }

    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("File cannot be empty");
        }

        if (file.getSize() > maxSize) {
            throw new RuntimeException("File size exceeds limit");
        }

        String contentType = file.getContentType();
        if (contentType == null || !FileUtils.isImage(contentType)) {
            throw new RuntimeException("Invalid file type");
        }
    }

    public List<Photo> getPublicPhotos() {
        return photoRepository.findByDeletedFalseAndIsPublicTrue();
    }

    public void deletePhoto(Long id) {
        Photo photo = photoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Photo not found"));
        photo.setDeleted(true);
        photoRepository.save(photo);
    }
}
