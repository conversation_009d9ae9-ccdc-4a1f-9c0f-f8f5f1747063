package com.example;

import com.example.entity.Photo;
import com.example.repository.PhotoRepository;
import com.example.service.PhotoService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class PhotoUploadDownloadApplicationTests {

    @Autowired
    private PhotoService photoService;

    @Autowired
    private PhotoRepository photoRepository;

    @Test
    public void testUploadAndDownload() throws IOException {
        // 准备测试文件
        Path testImagePath = Paths.get("src/test/resources/test-image.jpg");
        byte[] fileContent = Files.readAllBytes(testImagePath);
        MockMultipartFile testFile = new MockMultipartFile(
                "file",
                "test-image.jpg",
                "image/jpeg",
                fileContent
        );

        // 测试上传
        Photo uploadedPhoto = photoService.upload(testFile);
        assertNotNull(uploadedPhoto);
        assertNotNull(uploadedPhoto.getId());
        assertEquals("test-image.jpg", uploadedPhoto.getOriginalName());

        // 测试下载
        byte[] downloadedContent = photoService.download(uploadedPhoto.getFileName());
        assertArrayEquals(fileContent, downloadedContent);

        // 清理测试数据
        photoService.deletePhoto(uploadedPhoto.getId());
    }

    @Test
    public void testFileValidation() {
        // 测试空文件
        assertThrows(RuntimeException.class, () -> {
            photoService.upload(new MockMultipartFile("file", "", "", new byte[0]));
        });

        // 测试过大文件
        assertThrows(RuntimeException.class, () -> {
            byte[] largeFile = new byte[11 * 1024 * 1024]; // 11MB
            photoService.upload(new MockMultipartFile("file", "large.jpg", "image/jpeg", largeFile));
        });

        // 测试无效文件类型
        assertThrows(RuntimeException.class, () -> {
            photoService.upload(new MockMultipartFile("file", "test.txt", "text/plain", new byte[]{1, 2, 3}));
        });
    }
}
