package com.example.controller;

import com.example.entity.Photo;
import com.example.service.PhotoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class PhotoControllerTest {
    private MockMvc mockMvc;

    @InjectMocks
    private PhotoController photoController;

    @Mock
    private PhotoService photoService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(photoController).build();
    }

    @Test
    public void testUploadSingle() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile("file", "test.jpg", MediaType.IMAGE_JPEG_VALUE, "test image content".getBytes());
        
        Photo mockPhoto = new Photo();
        mockPhoto.setId(1L);
        mockPhoto.setFileName("test.jpg");
        
        when(photoService.upload(any(MockMultipartFile.class))).thenReturn(mockPhoto);
        
        // Act & Assert
        mockMvc.perform(multipart("/api/photos/upload").file(file))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").value(1L))
            .andExpect(jsonPath("$.fileName").value("test.jpg"));
            
        verify(photoService, times(1)).upload(any(MockMultipartFile.class));
    }

    @Test
    public void testUploadMultiple() throws Exception {
        // Arrange
        MockMultipartFile file1 = new MockMultipartFile("files", "test1.jpg", MediaType.IMAGE_JPEG_VALUE, "test image 1 content".getBytes());
        MockMultipartFile file2 = new MockMultipartFile("files", "test2.jpg", MediaType.IMAGE_JPEG_VALUE, "test image 2 content".getBytes());
        
        Photo photo1 = new Photo();
        photo1.setId(1L);
        photo1.setFileName("test1.jpg");
        
        Photo photo2 = new Photo();
        photo2.setId(2L);
        photo2.setFileName("test2.jpg");
        
        List<Photo> mockPhotos = Arrays.asList(photo1, photo2);
        
        when(photoService.uploadMultiple(any(MockMultipartFile[].class))).thenReturn(mockPhotos);
        
        // Act & Assert
        mockMvc.perform(multipart("/api/photos/upload/batch")
                .file(file1)
                .file(file2))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].id").value(1L))
            .andExpect(jsonPath("$[0].fileName").value("test1.jpg"))
            .andExpect(jsonPath("$[1].id").value(2L))
            .andExpect(jsonPath("$[1].fileName").value("test2.jpg"));
            
        verify(photoService, times(1)).uploadMultiple(any(MockMultipartFile[].class));
    }

    @Test
    public void testDownload() throws Exception {
        // Arrange
        String fileName = "test.jpg";
        byte[] fileContent = "test image content".getBytes();
        
        Photo mockPhoto = new Photo();
        mockPhoto.setId(1L);
        mockPhoto.setFileName(fileName);
        mockPhoto.setOriginalName("original_test.jpg");
        mockPhoto.setContentType(MediaType.IMAGE_JPEG_VALUE);
        
        when(photoService.download(eq(fileName))).thenReturn(fileContent);
        when(photoService.getPublicPhotos()).thenReturn(Arrays.asList(mockPhoto)); // 使用现有方法作为替代方案
        
        // Act & Assert
        mockMvc.perform(get("/api/photos/download/{fileName}", fileName))
            .andExpect(status().isOk())
            .andExpect(header().string(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE))
            .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"original_test.jpg\""));
            
        verify(photoService, times(1)).download(eq(fileName));
    }

    @Test
    public void testPreview() throws Exception {
        // Arrange
        String fileName = "test.jpg";
        byte[] fileContent = "test image content".getBytes();
        
        Photo mockPhoto = new Photo();
        mockPhoto.setId(1L);
        mockPhoto.setFileName(fileName);
        mockPhoto.setContentType(MediaType.IMAGE_JPEG_VALUE);
        
        when(photoService.download(eq(fileName))).thenReturn(fileContent);
        when(photoService.getPublicPhotos()).thenReturn(Arrays.asList(mockPhoto)); // 使用现有方法作为替代方案
        
        // Act & Assert
        mockMvc.perform(get("/api/photos/preview/{fileName}", fileName))
            .andExpect(status().isOk())
            .andExpect(header().string(HttpHeaders.CONTENT_TYPE, MediaType.IMAGE_JPEG_VALUE));
            
        verify(photoService, times(1)).download(eq(fileName));
    }

    @Test
    public void testGetPublicPhotos() throws Exception {
        // Arrange
        Photo photo1 = new Photo();
        photo1.setId(1L);
        photo1.setFileName("test1.jpg");
        
        Photo photo2 = new Photo();
        photo2.setId(2L);
        photo2.setFileName("test2.jpg");
        
        List<Photo> mockPhotos = Arrays.asList(photo1, photo2);
        
        when(photoService.getPublicPhotos()).thenReturn(mockPhotos);
        
        // Act & Assert
        mockMvc.perform(get("/api/photos/public"))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$[0].id").value(1L))
            .andExpect(jsonPath("$[0].fileName").value("test1.jpg"))
            .andExpect(jsonPath("$[1].id").value(2L))
            .andExpect(jsonPath("$[1].fileName").value("test2.jpg"));
            
        verify(photoService, times(1)).getPublicPhotos();
    }

    @Test
    public void testDeletePhoto() throws Exception {
        // Arrange
        Long photoId = 1L;
        
        // No need to do anything as the delete method is void
        
        // Act & Assert
        mockMvc.perform(delete("/api/photos/{id}", photoId))
            .andExpect(status().isNoContent());
            
        verify(photoService, times(1)).deletePhoto(eq(photoId));
    }
}