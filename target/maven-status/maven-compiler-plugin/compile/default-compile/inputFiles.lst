/Users/<USER>/code/newproject/src/main/java/com/example/config/GlobalExceptionHandler.java
/Users/<USER>/code/newproject/src/main/java/com/example/service/PhotoService.java
/Users/<USER>/code/newproject/src/main/java/com/example/exception/PhotoException.java
/Users/<USER>/code/newproject/src/main/java/com/example/entity/Photo.java
/Users/<USER>/code/newproject/src/main/java/com/example/PhotoUploadDownloadApplication.java
/Users/<USER>/code/newproject/src/main/java/com/example/config/SecurityConfig.java
/Users/<USER>/code/newproject/src/main/java/com/example/controller/PhotoController.java
/Users/<USER>/code/newproject/src/main/java/com/example/utils/FileUtils.java
/Users/<USER>/code/newproject/src/main/java/com/example/repository/PhotoRepository.java
